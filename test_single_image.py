#!/usr/bin/env python3
"""
Quick test script for testing a single image with RASM shadow removal model
"""

import os
import cv2
import numpy as np
import torch
import argparse
from utils.image_utils import load_img, save_img
import utils

def create_dummy_mask(image_path, mask_path):
    """Create a dummy mask for the image (assumes whole image needs shadow removal)"""
    img = cv2.imread(image_path)
    if img is None:
        raise ValueError(f"Could not load image: {image_path}")
    
    # Create a white mask (255 = shadow region)
    mask = np.ones((img.shape[0], img.shape[1]), dtype=np.uint8) * 255
    cv2.imwrite(mask_path, mask)
    print(f"Created dummy mask: {mask_path}")

def test_single_image(image_path, weights_path, output_dir="./single_test_results"):
    """Test a single image with the RASM model"""
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Create temporary test structure
    temp_dir = "./temp_test"
    test_a_dir = os.path.join(temp_dir, "test_A")
    test_b_dir = os.path.join(temp_dir, "test_B") 
    
    os.makedirs(test_a_dir, exist_ok=True)
    os.makedirs(test_b_dir, exist_ok=True)
    
    # Get image filename
    image_name = os.path.basename(image_path)
    
    # Copy image to test_A
    temp_image_path = os.path.join(test_a_dir, image_name)
    temp_mask_path = os.path.join(test_b_dir, image_name)
    
    # Copy the image
    import shutil
    shutil.copy2(image_path, temp_image_path)
    
    # Create dummy mask
    create_dummy_mask(image_path, temp_mask_path)
    
    # Run the test
    cmd = f"""python test.py --input_dir {temp_dir}/ \
--weights {weights_path} \
--result_dir {output_dir} \
--ex_name single_image_test \
--save_images"""
    
    print(f"Running command: {cmd}")
    os.system(cmd)
    
    # Clean up temp directory
    shutil.rmtree(temp_dir)
    
    print(f"Results saved to: {output_dir}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Test single image with RASM')
    parser.add_argument('--image', required=True, help='Path to input image')
    parser.add_argument('--weights', default='checkpoints/AISTD.pth', help='Path to model weights')
    parser.add_argument('--output', default='./single_test_results', help='Output directory')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image):
        print(f"Error: Image file not found: {args.image}")
        exit(1)
        
    if not os.path.exists(args.weights):
        print(f"Error: Weights file not found: {args.weights}")
        print("Available checkpoints:")
        if os.path.exists("checkpoints"):
            for f in os.listdir("checkpoints"):
                print(f"  - checkpoints/{f}")
        exit(1)
    
    test_single_image(args.image, args.weights, args.output)
